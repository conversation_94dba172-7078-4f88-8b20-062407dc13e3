java_import {
    name: "gson-prebuilt",
    jars: ["app/libs/gson-2.10.1.jar"],
    sdk_version: "current",
}

java_import {
    name: "eventbroker.api",
    jars: ["app/libs/vendor.thundercomm.eventbroker-V1-java.jar"],
    sdk_version: "current",
}

java_import {
    name: "fileencrypt.api",
    jars: ["app/libs/fileencrypt.api.jar"],
    sdk_version: "current",
}

// Then in your android_app block, change libs to module name:
android_app {
    name: "ConfigTool",
    vendor: true,
    platform_apis: true,
    privileged: true,
    sdk_version : "vendor_current",
    certificate: "platform",
    srcs: [
        "app/src/main/java/**/*.java",
    ],
    resource_dirs: ["app/src/main/res"],
    manifest: "app/src/main/AndroidManifest.xml",
    asset_dirs: ["app/src/main/assets"],

    static_libs: [
        "androidx.appcompat_appcompat",
        "androidx.annotation_annotation",
        "com.google.android.material_material",
        "androidx-constraintlayout_constraintlayout",
        "gson-prebuilt",
        "fileencrypt.api",
        "configtool.server.api",
        "eventbroker.api",
    ],

    optimize: {
        enabled: false,
    },

    system_ext_specific: false,
}