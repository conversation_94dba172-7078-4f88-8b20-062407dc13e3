#!/bin/bash

# 直接测试AIDL调用的脚本
# 由于Gradle编译问题，我们通过adb shell直接测试EventBroker连接

echo "=== TestApp AIDL Direct Test ==="
echo "Testing EventBroker AIDL interface..."

# 1. 检查EventBroker服务状态
echo ""
echo "1. Checking EventBroker service status:"
adb shell ps | grep -E "(brokeragent|eventprovider)"

# 2. 检查TestApp是否运行
echo ""
echo "2. Checking TestApp status:"
adb shell ps | grep testapp

# 3. 检查AIDL接口是否正确绑定
echo ""
echo "3. Checking service bindings:"
adb shell dumpsys activity services | grep -A 5 -B 5 "brokeragent"

# 4. 监听EventBroker相关日志
echo ""
echo "4. Monitoring EventBroker logs (press Ctrl+C to stop):"
echo "Please click 'SD Test' button in TestApp now..."
echo ""

# 清除日志并开始监听
adb shell logcat -c
adb shell logcat | grep -E "(EventBrokerClient|BrokerAgent|publishEvent|RuntimeErrorListener|SD.*error)" &
LOGCAT_PID=$!

# 等待用户操作
echo "Monitoring logs... Press Enter when done testing"
read -r

# 停止日志监听
kill $LOGCAT_PID 2>/dev/null

echo ""
echo "=== Test completed ==="
echo ""
echo "Expected behavior:"
echo "1. TestApp connects to EventBroker service ✓"
echo "2. Click 'SD Test' button"
echo "3. EventBrokerClient.sendSdCardError() called"
echo "4. IEventBroker.publishEvent() AIDL call"
echo "5. EventBroker processes event"
echo "6. RuntimeErrorListener receives event"
echo "7. Error broadcast sent"
echo ""
echo "If AIDL call fails, check:"
echo "- EventEntry Parcelable implementation"
echo "- AIDL interface compatibility"
echo "- Service permissions"
