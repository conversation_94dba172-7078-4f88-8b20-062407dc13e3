<?xml version="1.0" encoding="utf-8"?>
<!--
 * Copyright (C) 2023-2024 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *
 * Not a contribution.
-->
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.TestApp" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/white</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">12sp</item>
        <item name="buttonStyle">@style/SmallButtonStyle</item>
        <item name="android:buttonStyle">@style/SmallButtonStyle</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowActionBar">false</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>