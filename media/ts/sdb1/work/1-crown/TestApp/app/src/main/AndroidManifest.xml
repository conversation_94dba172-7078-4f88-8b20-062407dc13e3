<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.thundercomm.testapp">

    <!-- AppInfo测试权限 - 这些权限将通过AppInfo配置自动授予 -->
    <!-- 基础权限 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- Android 10+ 权限 -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- Android 11+ 权限 -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <!-- Android 12+ 蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

    <!-- Android 13+ 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- ConfigTool 配置变更广播权限 - 必须声明此权限才能接收配置变更广播 -->
    <uses-permission android:name="com.thundercomm.configtool.permission.CONFIG_CHANGED" />

    <!-- EventProvider 广播接收权限 - 必须声明此权限才能接收EventProvider广播 -->
    <uses-permission android:name="com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS" />

    <application
        android:allowBackup="true"
        android:icon="@android:drawable/ic_dialog_alert"
        android:roundIcon="@android:drawable/ic_dialog_alert"
        android:label="TestApp"
        android:supportsRtl="true"
        android:theme="@style/Theme.TestApp"
        tools:targetApi="30">
        
        <!-- MainActivity -->
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- EventProvider 错误信息广播接收器 - 改为动态注册以避免后台限制 -->

    </application>

</manifest>
