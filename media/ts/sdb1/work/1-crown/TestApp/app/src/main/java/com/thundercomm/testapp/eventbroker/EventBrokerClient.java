package com.thundercomm.testapp.eventbroker;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;

import vendor.thundercomm.eventbroker.EventBody;
import vendor.thundercomm.eventbroker.EventEntry;
import vendor.thundercomm.eventbroker.EventHeader;
import vendor.thundercomm.eventbroker.IEventBroker;

/**
 * EventBroker客户端
 * 用于连接EventBroker服务并发送事件
 * 模拟EventProvider的功能
 */
public class EventBrokerClient {
    private static final String TAG = "EventBrokerClient";
    
    // EventBroker服务信息
    private static final String BROKER_SERVICE_ACTION = "com.thundercomm.brokeragent.SERVICE";
    private static final String BROKER_SERVICE_PACKAGE = "vendor.thundercomm.brokeragent";
    private static final String BROKER_SERVICE_CLASS = "vendor.thundercomm.brokeragent.BrokerAgentService";
    
    // 事件常量（与EventProvider完全一致）
    private static final byte CATEGORY_RUNTIME_ERROR = 3;  // EventCategory.RUNTIME_ERROR
    private static final int HW_ID_SD_CARD = 9;
    private static final int ERROR_CODE_READ_WRITE_FAIL = 2;
    private static final int HW_ERROR_CODE_READ_WRITE_FAIL = 92;
    private static final int EVENT_LIFETIME_ONE_HOUR = 36000;  // 1 hour in seconds (not milliseconds)
    private static final byte PAYLOAD_TYPE_JSON = 0;  // PayloadType.JSON.getType() = 0
    
    private Context mContext;
    private IEventBroker mEventBroker;
    private boolean mIsConnected = false;
    private EventBrokerCallback mCallback;
    
    public interface EventBrokerCallback {
        void onConnected();
        void onDisconnected();
        void onConnectionFailed(String error);
    }
    
    public EventBrokerClient(Context context) {
        mContext = context;
    }
    
    public void setCallback(EventBrokerCallback callback) {
        mCallback = callback;
    }
    
    /**
     * 连接EventBroker服务
     */
    public void connect() {
        try {
            Intent intent = new Intent(BROKER_SERVICE_ACTION);
            intent.setClassName(BROKER_SERVICE_PACKAGE, BROKER_SERVICE_CLASS);
            
            boolean result = mContext.bindService(intent, mServiceConnection, Context.BIND_AUTO_CREATE);
            if (!result) {
                Log.e(TAG, "Failed to bind EventBroker service");
                if (mCallback != null) {
                    mCallback.onConnectionFailed("Failed to bind service");
                }
            } else {
                Log.d(TAG, "Binding EventBroker service...");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error connecting to EventBroker service", e);
            if (mCallback != null) {
                mCallback.onConnectionFailed("Connection error: " + e.getMessage());
            }
        }
    }
    
    /**
     * 断开EventBroker服务连接
     */
    public void disconnect() {
        try {
            if (mIsConnected) {
                mContext.unbindService(mServiceConnection);
                mIsConnected = false;
                mEventBroker = null;
                Log.d(TAG, "EventBroker service disconnected");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error disconnecting from EventBroker service", e);
        }
    }
    
    /**
     * 检查是否已连接
     */
    public boolean isConnected() {
        return mIsConnected && mEventBroker != null;
    }
    
    /**
     * 发送SD卡错误通知（模拟EventProvider的sendTestSdCardError）
     */
    public boolean sendSdCardError() {
        if (!isConnected()) {
            Log.e(TAG, "EventBroker not connected, cannot send SD card error");
            return false;
        }
        
        try {
            // 创建数据对象（与EventProvider的sendTestSdCardError完全一致）
            JSONObject dataObject = new JSONObject();
            dataObject.put("error_type", "read_write_fail");
            dataObject.put("test_trigger", true);
            dataObject.put("timestamp", System.currentTimeMillis());
            
            // 创建主JSON对象
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", ERROR_CODE_READ_WRITE_FAIL);
            jsonObject.put("hwErrorCode", HW_ERROR_CODE_READ_WRITE_FAIL);
            jsonObject.put("data", dataObject);
            
            // 创建事件条目（与EventProvider完全一致）
            EventEntry eventEntry = createJsonEventEntry(
                "sdcard_read_write_error",  // 与EventProvider相同的事件名称
                jsonObject,
                CATEGORY_RUNTIME_ERROR,
                HW_ID_SD_CARD
            );

            if (eventEntry == null) {
                Log.e(TAG, "Failed to create event entry");
                return false;
            }

            // 发布事件（真实调用）
            boolean success = publishEventEntry(eventEntry);
            if (success) {
                Log.i(TAG, "SD card error event sent successfully");
            } else {
                Log.e(TAG, "Failed to send SD card error event");
            }
            
            return success;
            
        } catch (JSONException e) {
            Log.e(TAG, "Failed to create SD card error JSON", e);
            return false;
        }
    }
    
    /**
     * 创建JSON事件条目（真实EventProvider格式）
     */
    private EventEntry createJsonEventEntry(String eventName, JSONObject jsonPayload,
                                           byte categoryId, int typeId) {
        try {
            // 创建事件体
            EventBody eventBody = new EventBody();
            eventBody.name = eventName;
            eventBody.payloadType = PAYLOAD_TYPE_JSON;

            try {
                byte[] bytes = jsonPayload.toString().getBytes(StandardCharsets.UTF_8);
                eventBody.payloadData = bytes;
                eventBody.payloadSize = bytes.length;  // 确保设置payloadSize
            } catch (Exception ex) {
                Log.e(TAG, "Failed to encode payload data", ex);
                return null;
            }

            // 创建事件头
            EventHeader eventHeader = new EventHeader();
            eventHeader.categoryId = categoryId;
            eventHeader.typeId = typeId;
            eventHeader.timestamp = System.currentTimeMillis();
            eventHeader.lifetime = EVENT_LIFETIME_ONE_HOUR;

            // 创建事件条目
            EventEntry eventEntry = new EventEntry();
            eventEntry.header = eventHeader;
            eventEntry.body = eventBody;

            return eventEntry;
        } catch (Exception ex) {
            Log.e(TAG, "Failed to create event entry", ex);
            return null;
        }
    }
    
    /**
     * 发布事件条目（真实调用EventBroker.publishEvent）
     */
    private boolean publishEventEntry(EventEntry eventEntry) {
        if (eventEntry == null) {
            Log.e(TAG, "EventEntry is null, cannot publish event");
            return false;
        }

        if (!isConnected()) {
            Log.e(TAG, "EventBroker not connected, cannot publish event");
            return false;
        }

        try {
            Log.i(TAG, "Publishing event via EventBroker:");
            Log.i(TAG, "  Event Name: " + eventEntry.body.name);
            Log.i(TAG, "  Category ID: " + eventEntry.header.categoryId);
            Log.i(TAG, "  Type ID: " + eventEntry.header.typeId);
            Log.i(TAG, "  Payload Size: " + eventEntry.body.payloadSize);
            Log.i(TAG, "  JSON Content: " + new String(eventEntry.body.payloadData, StandardCharsets.UTF_8));

            // 真实调用EventBroker.publishEvent
            mEventBroker.publishEvent(eventEntry);

            Log.i(TAG, "✅ Event published successfully via real AIDL interface!");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Failed to publish event via AIDL: " + e.getMessage(), e);
            Log.e(TAG, "Exception type: " + e.getClass().getSimpleName());
            if (e.getCause() != null) {
                Log.e(TAG, "Caused by: " + e.getCause().getMessage());
            }
            return false;
        }
    }
    
    // 服务连接回调
    private final ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "EventBroker service connected: " + name);
            Log.d(TAG, "Service IBinder: " + service);

            try {
                // 使用真实的AIDL接口
                mEventBroker = IEventBroker.Stub.asInterface(service);
                Log.d(TAG, "IEventBroker interface created: " + mEventBroker);
                mIsConnected = true;

                if (mCallback != null) {
                    mCallback.onConnected();
                }
            } catch (Exception e) {
                Log.e(TAG, "Failed to create IEventBroker interface", e);
                mIsConnected = false;
                if (mCallback != null) {
                    mCallback.onConnectionFailed("Failed to create interface: " + e.getMessage());
                }
            }
        }
        
        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "EventBroker service disconnected");
            mEventBroker = null;
            mIsConnected = false;
            
            if (mCallback != null) {
                mCallback.onDisconnected();
            }
        }
    };
}
