<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="2dp">

    <!-- Readable Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="AppInfo &amp; ConfigTool &amp; EventProvider Test"
        android:textSize="14sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="2dp" />

    <!-- Ultra Compact Button Grid -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="1dp">

        <Button
            android:id="@+id/checkPermissionsButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="Perm"
            android:textSize="10sp"
            android:padding="2dp"
            android:layout_marginEnd="1dp"
            android:minHeight="0dp"
            android:minWidth="0dp" />

        <Button
            android:id="@+id/checkDozeButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="Doze"
            android:textSize="10sp"
            android:padding="2dp"
            android:layout_marginEnd="1dp"
            android:minHeight="0dp"
            android:minWidth="0dp" />

        <Button
            android:id="@+id/clearLogButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="Clear"
            android:textSize="10sp"
            android:padding="2dp"
            android:minHeight="0dp"
            android:minWidth="0dp" />
    </LinearLayout>

    <!-- Second Row Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="1dp">

        <Button
            android:id="@+id/crashButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="Crash"
            android:textSize="10sp"
            android:padding="2dp"
            android:layout_marginEnd="1dp"
            android:minHeight="0dp"
            android:minWidth="0dp" />

        <Button
            android:id="@+id/anrButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="ANR"
            android:textSize="10sp"
            android:padding="2dp"
            android:minHeight="0dp"
            android:minWidth="0dp" />

        <!-- Empty space for balance -->
        <View
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1" />
    </LinearLayout>

    <!-- ConfigTool Test Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="1dp">

        <Button
            android:id="@+id/testConfigToolButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="Test CT"
            android:textSize="10sp"
            android:padding="2dp"
            android:layout_marginEnd="1dp"
            android:minHeight="0dp"
            android:minWidth="0dp" />

        <Button
            android:id="@+id/readConfigButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="Read"
            android:textSize="10sp"
            android:padding="2dp"
            android:layout_marginEnd="1dp"
            android:minHeight="0dp"
            android:minWidth="0dp" />

        <Button
            android:id="@+id/writeConfigButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="Write"
            android:textSize="10sp"
            android:padding="2dp"
            android:minHeight="0dp"
            android:minWidth="0dp" />
    </LinearLayout>

    <!-- EventProvider Test Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="1dp">

        <Button
            android:id="@+id/testEventProviderButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="Test EP"
            android:textSize="10sp"
            android:padding="2dp"
            android:layout_marginEnd="1dp"
            android:minHeight="0dp"
            android:minWidth="0dp" />

        <Button
            android:id="@+id/sendErrorBroadcastButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="Send Err"
            android:textSize="10sp"
            android:padding="2dp"
            android:layout_marginEnd="1dp"
            android:minHeight="0dp"
            android:minWidth="0dp" />

        <Button
            android:id="@+id/sendSdCardErrorButton"
            android:layout_width="0dp"
            android:layout_height="28dp"
            android:layout_weight="1"
            android:text="SD AIDL"
            android:textSize="10sp"
            android:padding="2dp"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:enabled="false" />
    </LinearLayout>

    <!-- Readable Log Display -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Log:"
        android:textSize="10sp"
        android:textStyle="bold"
        android:layout_marginBottom="2dp" />

    <!-- Readable ScrollView for log content -->
    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:background="#f8f8f8"
        android:padding="3dp"
        android:scrollbars="vertical">

        <TextView
            android:id="@+id/logTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="8sp"
            android:fontFamily="monospace"
            android:textColor="#333333"
            android:lineSpacingMultiplier="1.0" />
    </ScrollView>

</LinearLayout>

</ScrollView>
