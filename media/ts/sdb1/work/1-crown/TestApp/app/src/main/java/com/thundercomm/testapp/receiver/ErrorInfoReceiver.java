package com.thundercomm.testapp.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * EventProvider错误信息广播接收器
 * 接收来自EventProvider的硬件错误广播
 */
public class ErrorInfoReceiver extends BroadcastReceiver {
    private static final String TAG = "ErrorInfoReceiver";
    
    // EventProvider广播Action
    private static final String ACTION_ERROR_INFO = "yellowstone.ssolapp.ERROR_INFO";
    
    // 广播参数Key
    private static final String KEY_ERROR_CODE = "KEY_ERROR_CODE";
    private static final String KEY_ERROR_TIME = "KEY_ERROR_TIME";
    private static final String KEY_ERROR_SUMMARY = "KEY_ERROR_SUMMARY";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null || intent.getAction() == null) {
            return;
        }
        
        String action = intent.getAction();
        Log.d(TAG, "Received broadcast: " + action);
        
        if (ACTION_ERROR_INFO.equals(action)) {
            handleErrorInfo(context, intent);
        }
    }
    
    /**
     * 处理错误信息广播
     */
    private void handleErrorInfo(Context context, Intent intent) {
        try {
            // 提取广播参数
            String errorCode = intent.getStringExtra(KEY_ERROR_CODE);
            String errorTime = intent.getStringExtra(KEY_ERROR_TIME);
            String errorSummary = intent.getStringExtra(KEY_ERROR_SUMMARY);
            
            // 记录接收到的错误信息
            Log.i(TAG, "=== EventProvider Error Received ===");
            Log.i(TAG, "Error Code: " + errorCode);
            Log.i(TAG, "Error Time: " + errorTime);
            Log.i(TAG, "Error Summary: " + errorSummary);
            Log.i(TAG, "Received at: " + getCurrentTimestamp());
            
            // 解析错误码并获取错误描述
            String errorDescription = getErrorDescription(errorCode);
            Log.i(TAG, "Error Description: " + errorDescription);
            
            // 通知MainActivity显示错误信息
            notifyMainActivity(context, errorCode, errorTime, errorSummary, errorDescription);
            
        } catch (Exception e) {
            Log.e(TAG, "Error handling error info broadcast", e);
        }
    }
    
    /**
     * 根据错误码获取错误描述
     */
    private String getErrorDescription(String errorCode) {
        if (errorCode == null) {
            return "Unknown Error";
        }
        
        try {
            int code = Integer.parseInt(errorCode);
            switch (code) {
                // MCU错误
                case 13: return "MCU No Response";
                
                // LED错误
                case 21: return "LED Control Unavailable";
                
                // IMU错误
                case 31: return "G-Sensor Unavailable";
                case 41: return "Gyro Sensor Unavailable";
                
                // 摄像头错误
                case 51: return "Front Camera Unavailable";
                case 52: return "Front Camera No Signal";
                case 61: return "Incabin Camera Unavailable";
                case 62: return "Incabin Camera No Signal";
                case 70: return "Rear Camera Not Detected";
                case 71: return "Rear Camera Unavailable";
                case 72: return "Rear Camera No Signal";
                case 80: return "Option Camera Not Detected";
                case 81: return "Option Camera Unavailable";
                case 82: return "Option Camera No Signal";
                
                // SD卡错误
                case 90: return "No SD Card";
                case 91: return "SD Card Unavailable";
                case 92: return "SD Card Read/Write Fail";
                
                // LTE错误
                case 100: return "No SIM Card";
                case 101: return "LTE Module Unavailable";
                case 102: return "LTE Network Unavailable";
                
                // GNSS错误
                case 111: return "GNSS Fail Unavailable";
                case 112: return "DR Algorithm Unavailable";
                
                // 其他硬件错误
                case 121: return "BT Unavailable";
                case 131: return "WiFi Unavailable";
                case 141: return "I/F Box Unit Unavailable";
                case 155: return "CPU High Temperature";
                case 164: return "Battery Low Power";
                
                default: return "Unknown Hardware Error (Code: " + code + ")";
            }
        } catch (NumberFormatException e) {
            return "Invalid Error Code: " + errorCode;
        }
    }
    
    /**
     * 通知MainActivity显示错误信息
     */
    private void notifyMainActivity(Context context, String errorCode, String errorTime, 
                                   String errorSummary, String errorDescription) {
        try {
            // 发送本地广播给MainActivity
            Intent localIntent = new Intent("com.thundercomm.testapp.ERROR_RECEIVED");
            localIntent.putExtra("errorCode", errorCode);
            localIntent.putExtra("errorTime", errorTime);
            localIntent.putExtra("errorSummary", errorSummary);
            localIntent.putExtra("errorDescription", errorDescription);
            localIntent.putExtra("receivedTime", getCurrentTimestamp());
            
            // 发送本地广播
            context.sendBroadcast(localIntent);
            
        } catch (Exception e) {
            Log.e(TAG, "Error sending local broadcast", e);
        }
    }
    
    /**
     * 获取当前时间戳
     */
    private String getCurrentTimestamp() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
                .format(new Date());
    }
}
