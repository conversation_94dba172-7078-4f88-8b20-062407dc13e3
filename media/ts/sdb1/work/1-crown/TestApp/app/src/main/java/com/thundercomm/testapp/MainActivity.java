package com.thundercomm.testapp;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.thundercomm.configtool.api.ConfigToolServiceManager;
import com.thundercomm.configtool.api.IConfigToolServiceConnectCallback;
import com.thundercomm.testapp.eventbroker.EventBrokerClient;
import com.thundercomm.testapp.sender.TestErrorSender;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * AppInfo测试应用主界面
 * 用于测试AppInfo配置功能，包括：
 * 1. 权限自动授予测试
 * 2. Doze白名单测试
 * 3. 应用状态显示
 * 4. ConfigTool 集成测试
 * 5. 配置变更广播测试
 */
public class MainActivity extends AppCompatActivity {
    private static final String TAG = "TestApp";

    // API级别常量
    private static final int API_LEVEL_Q = 29;          // Android 10
    private static final int API_LEVEL_R = 30;          // Android 11
    private static final int API_LEVEL_S = 31;          // Android 12
    private static final int API_LEVEL_TIRAMISU = 33;   // Android 13

    private TextView mLogTextView;
    private ScrollView mScrollView;
    private Button mCheckPermissionsButton;
    private Button mCheckDozeButton;
    private Button mClearLogButton;
    private Button mCrashButton;
    private Button mAnrButton;
    private Button mTestConfigToolButton;
    private Button mReadConfigButton;
    private Button mWriteConfigButton;
    private Button mTestEventProviderButton;
    private Button mSendErrorBroadcastButton;
    private Button mSendSdCardErrorButton;
    private Handler mHandler;

    // EventProvider 错误广播接收器
    private BroadcastReceiver mErrorReceiver;
    private BroadcastReceiver mEventProviderErrorReceiver;

    // 错误广播发送器
    private TestErrorSender mErrorSender;

    // EventBroker客户端
    private EventBrokerClient mEventBrokerClient;
    private boolean mEventBrokerConnected = false;

    // ConfigTool 相关
    private ConfigToolServiceManager mConfigManager;
    private boolean mConfigServiceConnected = false;

    // ConfigTool 服务连接回调
    private final IConfigToolServiceConnectCallback mConfigCallback = new IConfigToolServiceConnectCallback() {
        @Override
        public void onServiceConnected() {
            addLog("🔗 ConfigTool service connected successfully!");
            mConfigServiceConnected = true;
            runOnUiThread(() -> {
                mReadConfigButton.setEnabled(true);
                mWriteConfigButton.setEnabled(true);
            });
        }

        @Override
        public void onServiceDisconnected() {
            addLog("❌ ConfigTool service disconnected");
            mConfigServiceConnected = false;
            runOnUiThread(() -> {
                mReadConfigButton.setEnabled(false);
                mWriteConfigButton.setEnabled(false);
            });
        }

        @Override
        public void onConfigChanged(String modifiedFields, String addedFields, long timestamp) {
            addLog("📢 Configuration changed broadcast received!");
            addLog("  Timestamp: " + new SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()).format(new Date(timestamp)));

            if (modifiedFields != null && !modifiedFields.isEmpty()) {
                addLog("  Modified fields: " + modifiedFields);
            }

            if (addedFields != null && !addedFields.isEmpty()) {
                addLog("  Added fields: " + addedFields);
            }

            addLog("✅ Broadcast test PASSED - Protected broadcast working!");
        }
    };

    // 动态权限列表 - 根据API级别添加
    private List<String> getTestPermissions() {
        List<String> permissions = new ArrayList<>();

        // 基础权限
        permissions.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        permissions.add(Manifest.permission.ACCESS_FINE_LOCATION);
        permissions.add(Manifest.permission.CAMERA);
        permissions.add(Manifest.permission.RECORD_AUDIO);
        permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);

        // Android 11+ 权限
        if (Build.VERSION.SDK_INT >= API_LEVEL_R) {
            permissions.add(Manifest.permission.MANAGE_EXTERNAL_STORAGE);
        }

        // Android 12+ 蓝牙权限
        if (Build.VERSION.SDK_INT >= API_LEVEL_S) {
            permissions.add("android.permission.BLUETOOTH_CONNECT");
            permissions.add("android.permission.BLUETOOTH_SCAN");
        }

        // Android 13+ 通知权限
        if (Build.VERSION.SDK_INT >= API_LEVEL_TIRAMISU) {
            permissions.add("android.permission.POST_NOTIFICATIONS");
        }

        // 后台位置权限 (Android 10+)
        if (Build.VERSION.SDK_INT >= API_LEVEL_Q) {
            permissions.add(Manifest.permission.ACCESS_BACKGROUND_LOCATION);
        }

        return permissions;
    }

    /**
     * 检查权限状态（包括特殊权限）
     */
    private boolean checkPermissionStatus(String permission) {
        switch (permission) {
            case Manifest.permission.MANAGE_EXTERNAL_STORAGE:
                // MANAGE_EXTERNAL_STORAGE需要特殊检查
                if (Build.VERSION.SDK_INT >= API_LEVEL_R) {
                    return android.os.Environment.isExternalStorageManager();
                } else {
                    // Android 11以下版本不需要此权限
                    return true;
                }
//
//            case "android.permission.SYSTEM_ALERT_WINDOW":
//                // 系统悬浮窗权限
//                if (Build.VERSION.SDK_INT >= 23) {
//                    return android.provider.Settings.canDrawOverlays(this);
//                } else {
//                    return checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED;
//                }
//
//            case "android.permission.WRITE_SETTINGS":
//                // 修改系统设置权限
//                if (Build.VERSION.SDK_INT >= 23) {
//                    return android.provider.Settings.System.canWrite(this);
//                } else {
//                    return checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED;
//                }

            default:
                // 普通运行时权限
                return checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initViews();
        setupClickListeners();
        initConfigTool();
        initEventProviderReceiver();
        initEventBrokerClient();

        addLog("TestApp started - AppInfo & ConfigTool & EventProvider & EventBroker Testing Ready");
        addLog("Package: " + getPackageName());
        addLog("===========================================");
    }

    private void initViews() {
        mLogTextView = findViewById(R.id.logTextView);
        mScrollView = findViewById(R.id.scrollView);
        mCheckPermissionsButton = findViewById(R.id.checkPermissionsButton);
        mCheckDozeButton = findViewById(R.id.checkDozeButton);
        mClearLogButton = findViewById(R.id.clearLogButton);
        mCrashButton = findViewById(R.id.crashButton);
        mAnrButton = findViewById(R.id.anrButton);
        mTestConfigToolButton = findViewById(R.id.testConfigToolButton);
        mReadConfigButton = findViewById(R.id.readConfigButton);
        mWriteConfigButton = findViewById(R.id.writeConfigButton);
        mTestEventProviderButton = findViewById(R.id.testEventProviderButton);
        mSendErrorBroadcastButton = findViewById(R.id.sendErrorBroadcastButton);
        mSendSdCardErrorButton = findViewById(R.id.sendSdCardErrorButton);
        mHandler = new Handler(Looper.getMainLooper());

        // 初始状态下禁用配置操作按钮
        mReadConfigButton.setEnabled(false);
        mWriteConfigButton.setEnabled(false);
    }

    private void setupClickListeners() {
        mCheckPermissionsButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                checkAllPermissions();
            }
        });

        mCheckDozeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                checkDozeWhitelistStatus();
            }
        });

        mClearLogButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clearLog();
            }
        });

        mCrashButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                simulateCrash();
            }
        });

        mAnrButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                simulateANR();
            }
        });

        mTestConfigToolButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testConfigToolConnection();
            }
        });

        mReadConfigButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testReadConfig();
            }
        });

        mWriteConfigButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testWriteConfig();
            }
        });

        mTestEventProviderButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testEventProviderBroadcast();
            }
        });

        mSendErrorBroadcastButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                sendTestErrorBroadcast();
            }
        });

        mSendSdCardErrorButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                sendSdCardErrorViaAidl();
            }
        });
    }

    /**
     * 初始化 ConfigTool
     */
    private void initConfigTool() {
        addLog("=== Initializing ConfigTool ===");
        try {
            mConfigManager = ConfigToolServiceManager.getInstance();
            mConfigManager.setServiceConnectCallback(mConfigCallback);
            mConfigManager.bindService(this);
            addLog("ConfigTool binding initiated...");
        } catch (Exception e) {
            addLog("❌ Failed to initialize ConfigTool: " + e.getMessage());
        }
        addLog("===========================================");
    }

    /**
     * 初始化 EventProvider 错误广播接收器
     */
    private void initEventProviderReceiver() {
        addLog("=== Initializing EventProvider Receiver ===");

        // 本地广播接收器（用于显示错误信息）
        mErrorReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if ("com.thundercomm.testapp.ERROR_RECEIVED".equals(intent.getAction())) {
                    String errorCode = intent.getStringExtra("errorCode");
                    String errorTime = intent.getStringExtra("errorTime");
                    String errorSummary = intent.getStringExtra("errorSummary");
                    String errorDescription = intent.getStringExtra("errorDescription");
                    String receivedTime = intent.getStringExtra("receivedTime");

                    addLog("🚨 EventProvider Error Received!");
                    addLog("  Error Code: " + errorCode);
                    addLog("  Error Description: " + errorDescription);
                    addLog("  Error Time: " + errorTime);
                    addLog("  Received Time: " + receivedTime);
                    if (errorSummary != null && !errorSummary.isEmpty()) {
                        addLog("  Summary: " + errorSummary);
                    }
                    addLog("✅ EventProvider broadcast test PASSED!");

                    showToast("EventProvider Error: " + errorDescription);
                }
            }
        };

        // EventProvider错误广播接收器（动态注册以避免后台限制）
        mEventProviderErrorReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if ("yellowstone.ssolapp.ERROR_INFO".equals(intent.getAction())) {
                    String errorCode = intent.getStringExtra("KEY_ERROR_CODE");
                    String errorTime = intent.getStringExtra("KEY_ERROR_TIME");
                    String errorSummary = intent.getStringExtra("KEY_ERROR_SUMMARY");

                    addLog("📡 Direct EventProvider broadcast received!");
                    addLog("  Error Code: " + errorCode);
                    addLog("  Error Time: " + errorTime);
                    addLog("  Error Summary: " + errorSummary);

                    // 解析错误描述
                    String errorDescription = getErrorDescription(errorCode);
                    addLog("  Error Description: " + errorDescription);

                    // 发送本地广播
                    Intent localIntent = new Intent("com.thundercomm.testapp.ERROR_RECEIVED");
                    localIntent.putExtra("errorCode", errorCode);
                    localIntent.putExtra("errorTime", errorTime);
                    localIntent.putExtra("errorSummary", errorSummary);
                    localIntent.putExtra("errorDescription", errorDescription);
                    localIntent.putExtra("receivedTime", getCurrentTimestamp());
                    sendBroadcast(localIntent);
                }
            }
        };

        // 注册本地广播接收器
        IntentFilter localFilter = new IntentFilter("com.thundercomm.testapp.ERROR_RECEIVED");
        registerReceiver(mErrorReceiver, localFilter);

        // 动态注册EventProvider错误广播接收器
        IntentFilter errorFilter = new IntentFilter("yellowstone.ssolapp.ERROR_INFO");
        registerReceiver(mEventProviderErrorReceiver, errorFilter);

        addLog("✅ EventProvider receivers registered (dynamic registration)");
        addLog("This avoids Android 8.0+ background execution limitations");
        addLog("===========================================");
    }

    /**
     * 测试 EventProvider 广播接收
     */
    private void testEventProviderBroadcast() {
        addLog("=== Testing EventProvider Broadcast Reception ===");
        addLog("This test checks if we can receive EventProvider error broadcasts");
        addLog("Make sure EventProvider is running and sending error broadcasts");
        addLog("");
        addLog("To trigger test errors from EventProvider:");
        addLog("1. Open EventProvider app");
        addLog("2. Click 'Test SD Card Error' button");
        addLog("3. Or click other error simulation buttons");
        addLog("");
        addLog("Waiting for EventProvider broadcasts...");
        addLog("===========================================");

        showToast("Waiting for EventProvider broadcasts...");
    }

    /**
     * 初始化 EventBroker 客户端
     */
    private void initEventBrokerClient() {
        addLog("=== Initializing EventBroker Client ===");

        mEventBrokerClient = new EventBrokerClient(this);
        mEventBrokerClient.setCallback(new EventBrokerClient.EventBrokerCallback() {
            @Override
            public void onConnected() {
                mEventBrokerConnected = true;
                addLog("✅ EventBroker client connected!");
                runOnUiThread(() -> {
                    if (mSendSdCardErrorButton != null) {
                        mSendSdCardErrorButton.setEnabled(true);
                    }
                });
            }

            @Override
            public void onDisconnected() {
                mEventBrokerConnected = false;
                addLog("❌ EventBroker client disconnected");
                runOnUiThread(() -> {
                    if (mSendSdCardErrorButton != null) {
                        mSendSdCardErrorButton.setEnabled(false);
                    }
                });
            }

            @Override
            public void onConnectionFailed(String error) {
                mEventBrokerConnected = false;
                addLog("❌ EventBroker connection failed: " + error);
                runOnUiThread(() -> {
                    if (mSendSdCardErrorButton != null) {
                        mSendSdCardErrorButton.setEnabled(false);
                    }
                });
            }
        });

        // 连接EventBroker服务
        mEventBrokerClient.connect();

        addLog("EventBroker client connecting...");
        addLog("===========================================");
    }

    /**
     * 通过AIDL发送SD卡错误通知（真正的AIDL调用）
     */
    private void sendSdCardErrorViaAidl() {
        addLog("=== Sending SD Card Error via AIDL ===");
        addLog("Using real AIDL interface to EventBroker...");

        if (!mEventBrokerConnected) {
            addLog("❌ EventBroker not connected");
            showToast("EventBroker not connected");
            return;
        }

        boolean success = mEventBrokerClient.sendSdCardError();

        if (success) {
            addLog("✅ SD card error sent via AIDL successfully!");
            addLog("  Event Name: sdcard_read_write_error");  // 与EventProvider一致
            addLog("  Category: RUNTIME_ERROR (3)");  // 正确的类别ID
            addLog("  HW ID: SD_CARD (9)");
            addLog("  Error Code: READ_WRITE_FAIL (2)");
            addLog("  HW Error Code: 92");
            addLog("  PayloadType: JSON (0)");  // 正确的PayloadType
            addLog("");
            addLog("This uses real AIDL call to EventBroker:");
            addLog("TestApp -> IEventBroker.publishEvent() -> EventBroker");
            addLog("EventBroker -> RuntimeErrorListener -> ErrorInfoSender");
            addLog("");
            addLog("Event format matches EventProvider exactly!");
            addLog("Watch RuntimeErrorListener logs for event processing...");

            showToast("SD card error sent via AIDL");
        } else {
            addLog("❌ Failed to send SD card error via AIDL");
            addLog("Check EventBrokerClient logs for detailed error info");
            showToast("Failed to send SD card error");
        }

        addLog("===========================================");
    }

    /**
     * 发送测试错误广播
     * 模拟EventProvider发送错误通知
     */
    private void sendTestErrorBroadcast() {
        addLog("=== Sending Test Error Broadcast ===");
        addLog("Simulating EventProvider error notification...");

        try {
            // 模拟不同类型的硬件错误
            String[] errorCodes = {"92", "51", "121", "155", "13"};
            String[] errorDescriptions = {
                "SD Card Read/Write Fail",
                "Front Camera Unavailable",
                "BT Unavailable",
                "CPU High Temperature",
                "MCU No Response"
            };

            // 随机选择一个错误类型
            int randomIndex = (int) (Math.random() * errorCodes.length);
            String errorCode = errorCodes[randomIndex];
            String errorDescription = errorDescriptions[randomIndex];

            // 创建错误广播Intent
            Intent errorIntent = new Intent("yellowstone.ssolapp.ERROR_INFO");
            errorIntent.putExtra("KEY_ERROR_CODE", errorCode);
            errorIntent.putExtra("KEY_ERROR_TIME", getCurrentTimestamp());
            errorIntent.putExtra("KEY_ERROR_SUMMARY", "Test error from TestApp");

            // 发送带权限的广播
            sendBroadcast(errorIntent, "com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS");

            addLog("✅ Error broadcast sent successfully!");
            addLog("  Error Code: " + errorCode);
            addLog("  Error Type: " + errorDescription);
            addLog("  Action: yellowstone.ssolapp.ERROR_INFO");
            addLog("  Permission: com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS");
            addLog("");
            addLog("This broadcast should be received by:");
            addLog("1. TestApp itself (if receiver is working)");
            addLog("2. Any other app with RECEIVE_BROADCASTS permission");

            showToast("Test error broadcast sent: " + errorDescription);

        } catch (Exception e) {
            addLog("❌ Failed to send error broadcast: " + e.getMessage());
            showToast("Failed to send broadcast: " + e.getMessage());
        }

        addLog("===========================================");
    }

    /**
     * 获取当前时间戳 (EventProvider格式)
     */
    private String getCurrentTimestamp() {
        return new SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(new Date());
    }

    /**
     * 根据错误码获取错误描述
     */
    private String getErrorDescription(String errorCode) {
        if (errorCode == null) {
            return "Unknown Error";
        }

        try {
            int code = Integer.parseInt(errorCode);
            switch (code) {
                // MCU错误
                case 13: return "MCU No Response";

                // LED错误
                case 21: return "LED Control Unavailable";

                // IMU错误
                case 31: return "G-Sensor Unavailable";
                case 41: return "Gyro Sensor Unavailable";

                // 摄像头错误
                case 51: return "Front Camera Unavailable";
                case 52: return "Front Camera No Signal";
                case 61: return "Incabin Camera Unavailable";
                case 62: return "Incabin Camera No Signal";
                case 70: return "Rear Camera Not Detected";
                case 71: return "Rear Camera Unavailable";
                case 72: return "Rear Camera No Signal";
                case 80: return "Option Camera Not Detected";
                case 81: return "Option Camera Unavailable";
                case 82: return "Option Camera No Signal";

                // SD卡错误
                case 90: return "No SD Card";
                case 91: return "SD Card Unavailable";
                case 92: return "SD Card Read/Write Fail";

                // LTE错误
                case 100: return "No SIM Card";
                case 101: return "LTE Module Unavailable";
                case 102: return "LTE Network Unavailable";

                // GNSS错误
                case 111: return "GNSS Fail Unavailable";
                case 112: return "DR Algorithm Unavailable";

                // 其他硬件错误
                case 121: return "BT Unavailable";
                case 131: return "WiFi Unavailable";
                case 141: return "I/F Box Unit Unavailable";
                case 155: return "CPU High Temperature";
                case 164: return "Battery Low Power";

                default: return "Unknown Hardware Error (Code: " + code + ")";
            }
        } catch (NumberFormatException e) {
            return "Invalid Error Code: " + errorCode;
        }
    }

    /**
     * 测试 ConfigTool 连接状态
     */
    private void testConfigToolConnection() {
        addLog("=== Testing ConfigTool Connection ===");

        if (mConfigManager == null) {
            addLog("❌ ConfigManager is null");
            return;
        }

        boolean isDisconnected = mConfigManager.isServiceDisconnected();
        addLog("Service disconnected: " + isDisconnected);
        addLog("Local connection flag: " + mConfigServiceConnected);

        if (mConfigServiceConnected && !isDisconnected) {
            addLog("✅ ConfigTool service is connected and ready!");
            showToast("ConfigTool service connected");
        } else {
            addLog("⚠️ ConfigTool service not connected");
            showToast("ConfigTool service not connected");

            // 尝试重新连接
            addLog("Attempting to reconnect...");
            try {
                mConfigManager.bindService(this);
            } catch (Exception e) {
                addLog("❌ Reconnection failed: " + e.getMessage());
            }
        }

        addLog("===========================================");
    }

    /**
     * 测试读取配置
     */
    private void testReadConfig() {
        addLog("=== Testing Read Configuration ===");

        if (!mConfigServiceConnected) {
            addLog("❌ Service not connected");
            showToast("Service not connected");
            return;
        }

        try {
            // 测试读取一些配置项
            String[] testKeys = {
                "logging",
                "agps",
                "sim",
                "recMode",
                "appInfo[0].packageName"
            };

            for (String key : testKeys) {
                String value = mConfigManager.getConfigValue(key);
                addLog("  " + key + " = " + value);
            }

            addLog("✅ Read configuration test completed");
            showToast("Read test completed");

        } catch (Exception e) {
            addLog("❌ Read configuration failed: " + e.getMessage());
            showToast("Read failed: " + e.getMessage());
        }

        addLog("===========================================");
    }

    /**
     * 测试写入配置 - 这将触发配置变更广播
     */
    private void testWriteConfig() {
        addLog("=== Testing Write Configuration ===");
        addLog("This will trigger a configuration change broadcast!");

        if (!mConfigServiceConnected) {
            addLog("❌ Service not connected");
            showToast("Service not connected");
            return;
        }

        try {
            // 创建测试配置 - 修改一些现有值并添加新值
            String testConfig = "{\n" +
                "  \"logging\": false,\n" +
                "  \"testTimestamp\": " + System.currentTimeMillis() + ",\n" +
                "  \"testApp\": {\n" +
                "    \"name\": \"TestApp\",\n" +
                "    \"version\": \"1.0\",\n" +
                "    \"testTime\": \"" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new Date()) + "\"\n" +
                "  }\n" +
                "}";

            addLog("Writing test configuration:");
            addLog(testConfig);

            boolean success = mConfigManager.saveConfig(testConfig);

            if (success) {
                addLog("✅ Write configuration successful!");
                addLog("🔔 Watch for configuration change broadcast...");
                showToast("Write successful - watch for broadcast");
            } else {
                addLog("❌ Write configuration failed");
                showToast("Write failed");
            }

        } catch (Exception e) {
            addLog("❌ Write configuration error: " + e.getMessage());
            showToast("Write error: " + e.getMessage());
        }

        addLog("===========================================");
    }

    /**
     * 检查所有测试权限
     */
    private void checkAllPermissions() {
        addLog("=== Checking All Permissions ===");
        addLog("Android API Level: " + Build.VERSION.SDK_INT);

        List<String> testPermissions = getTestPermissions();
        int grantedCount = 0;
        int totalCount = testPermissions.size();

        for (String permission : testPermissions) {
            boolean isGranted = checkPermissionStatus(permission);
            String status = isGranted ? "✓ GRANTED" : "✗ DENIED";
            String permissionName = permission.substring(permission.lastIndexOf('.') + 1);

            addLog(permissionName + ": " + status);

            if (isGranted) {
                grantedCount++;
            }
        }

        addLog("=== Permission Summary ===");
        addLog("Granted: " + grantedCount + "/" + totalCount);

        if (grantedCount == totalCount) {
            addLog("🎉 ALL PERMISSIONS GRANTED! AppInfo config working!");
            showToast("All permissions granted!");
        } else {
            addLog("⚠️ Some permissions missing. Check AppInfo config.");
            showToast("Some permissions missing: " + (totalCount - grantedCount));
        }

        addLog("===========================================");

        // 额外检查AppOps权限状态
        checkAppOpsPermissions();
    }

    /**
     * 检查AppOps权限状态
     */
    private void checkAppOpsPermissions() {
        addLog("=== Checking AppOps Permissions ===");

        try {
            // 检查MANAGE_EXTERNAL_STORAGE的AppOps状态
            addLog("MANAGE_EXTERNAL_STORAGE AppOps check:");
            addLog("  Environment.isExternalStorageManager(): " + Environment.isExternalStorageManager());

//            // 检查其他特殊权限
//            if (Build.VERSION.SDK_INT >= 23) {
//                addLog("SYSTEM_ALERT_WINDOW: " + (Settings.canDrawOverlays(this) ? "✓ GRANTED" : "✗ DENIED"));
//                addLog("WRITE_SETTINGS: " + (Settings.System.canWrite(this) ? "✓ GRANTED" : "✗ DENIED"));
//            }

        } catch (Exception e) {
            addLog("❌ Error checking AppOps permissions: " + e.getMessage());
        }

        addLog("===========================================");
    }

    /**
     * 检查Doze白名单状态
     */
    private void checkDozeWhitelistStatus() {
        addLog("=== Checking Doze Whitelist Status ===");

        try {
            PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
            if (pm != null) {
                boolean isWhitelisted = pm.isIgnoringBatteryOptimizations(getPackageName());
                String status = isWhitelisted ? "✓ WHITELISTED" : "✗ NOT WHITELISTED";

                addLog("Doze Whitelist: " + status);

                if (isWhitelisted) {
                    addLog("🎉 App is in Doze whitelist! AppInfo config working!");
                    showToast("App is in Doze whitelist!");
                } else {
                    addLog("⚠️ App not in Doze whitelist. Check AppInfo config.");
                    showToast("App not in Doze whitelist");
                }
            } else {
                addLog("❌ PowerManager not available");
            }
        } catch (Exception e) {
            addLog("❌ Error checking Doze status: " + e.getMessage());
        }

        addLog("===========================================");
    }

    /**
     * 模拟应用崩溃
     * 3秒后触发空指针异常
     */
    private void simulateCrash() {
        addLog("=== Crash Test ===");
        addLog("App will crash in 3 seconds...");
        showToast("应用将在3秒后崩溃");

        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                addLog("Triggering crash now...");
                // 故意引发空指针异常
                String nullString = null;
                int length = nullString.length(); // 这里会崩溃
                System.out.println("这行代码永远不会执行: " + length);
            }
        }, 3000);
    }

    /**
     * 模拟应用无响应(ANR)
     * 阻塞主线程20秒
     */
    private void simulateANR() {
        addLog("=== ANR Test ===");
        addLog("Blocking main thread for 20 seconds...");
        showToast("主线程将被阻塞20秒，导致ANR");

        try {
            // 阻塞主线程20秒
            Thread.sleep(20000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 如果系统未强制关闭应用，会执行到这里
        addLog("ANR test completed, app responsive again");
        showToast("阻塞结束，应用恢复响应");
    }

    /**
     * 添加日志
     */
    private void addLog(String message) {
        Log.d(TAG,"--------->" + message);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                String timestamp = new SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(new Date());
                String logEntry = "[" + timestamp + "] " + message + "\n";

                mLogTextView.append(logEntry);

                // 自动滚动到底部
                mScrollView.post(new Runnable() {
                    @Override
                    public void run() {
                        mScrollView.fullScroll(View.FOCUS_DOWN);
                    }
                });
            }
        });
    }

    /**
     * 清除日志
     */
    private void clearLog() {
        mLogTextView.setText("");
        addLog("Log cleared");
    }

    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理 ConfigTool 连接
        if (mConfigManager != null) {
            try {
                mConfigManager.unbindService(this);
                addLog("ConfigTool service unbound");
            } catch (Exception e) {
                Log.e(TAG, "Error unbinding ConfigTool service", e);
            }
        }

        // 清理 EventProvider 广播接收器
        if (mErrorReceiver != null) {
            try {
                unregisterReceiver(mErrorReceiver);
                addLog("Local error receiver unregistered");
            } catch (Exception e) {
                Log.e(TAG, "Error unregistering local error receiver", e);
            }
        }

        if (mEventProviderErrorReceiver != null) {
            try {
                unregisterReceiver(mEventProviderErrorReceiver);
                addLog("EventProvider error receiver unregistered");
            } catch (Exception e) {
                Log.e(TAG, "Error unregistering EventProvider error receiver", e);
            }
        }

        // 清理 EventBroker 客户端连接
        if (mEventBrokerClient != null) {
            try {
                mEventBrokerClient.disconnect();
                addLog("EventBroker client disconnected");
            } catch (Exception e) {
                Log.e(TAG, "Error disconnecting EventBroker client", e);
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        addLog("App resumed - Ready for testing");
    }
} 