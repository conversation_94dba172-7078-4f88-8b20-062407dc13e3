package com.thundercomm.testapp.sender;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Random;

/**
 * 测试错误广播发送器
 * 模拟EventProvider发送各种硬件错误广播
 */
public class TestErrorSender {
    private static final String TAG = "TestErrorSender";
    
    // EventProvider广播常量
    private static final String ACTION_ERROR_INFO = "yellowstone.ssolapp.ERROR_INFO";
    private static final String KEY_ERROR_CODE = "KEY_ERROR_CODE";
    private static final String KEY_ERROR_TIME = "KEY_ERROR_TIME";
    private static final String KEY_ERROR_SUMMARY = "KEY_ERROR_SUMMARY";
    private static final String PERMISSION_RECEIVE_BROADCASTS = "com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS";
    
    private final Context mContext;
    private final Random mRandom;
    
    // 预定义的错误类型
    private static final ErrorType[] ERROR_TYPES = {
        new ErrorType("13", "MCU No Response", "Critical system error"),
        new ErrorType("21", "LED Control Unavailable", "LED system failure"),
        new ErrorType("31", "G-Sensor Unavailable", "Accelerometer sensor error"),
        new ErrorType("41", "Gyro Sensor Unavailable", "Gyroscope sensor error"),
        new ErrorType("51", "Front Camera Unavailable", "Front camera system error"),
        new ErrorType("52", "Front Camera No Signal", "Front camera signal lost"),
        new ErrorType("61", "Incabin Camera Unavailable", "Interior camera system error"),
        new ErrorType("62", "Incabin Camera No Signal", "Interior camera signal lost"),
        new ErrorType("70", "Rear Camera Not Detected", "Rear camera not found"),
        new ErrorType("71", "Rear Camera Unavailable", "Rear camera system error"),
        new ErrorType("72", "Rear Camera No Signal", "Rear camera signal lost"),
        new ErrorType("80", "Option Camera Not Detected", "Optional camera not found"),
        new ErrorType("81", "Option Camera Unavailable", "Optional camera system error"),
        new ErrorType("82", "Option Camera No Signal", "Optional camera signal lost"),
        new ErrorType("90", "No SD Card", "SD card not inserted"),
        new ErrorType("91", "SD Card Unavailable", "SD card system error"),
        new ErrorType("92", "SD Card Read/Write Fail", "SD card I/O error"),
        new ErrorType("100", "No SIM Card", "SIM card not inserted"),
        new ErrorType("101", "LTE Module Unavailable", "LTE module system error"),
        new ErrorType("102", "LTE Network Unavailable", "LTE network connection lost"),
        new ErrorType("111", "GNSS Fail Unavailable", "GPS system error"),
        new ErrorType("112", "DR Algorithm Unavailable", "Dead reckoning algorithm error"),
        new ErrorType("121", "BT Unavailable", "Bluetooth system error"),
        new ErrorType("131", "WiFi Unavailable", "WiFi system error"),
        new ErrorType("141", "I/F Box Unit Unavailable", "Interface box unit error"),
        new ErrorType("155", "CPU High Temperature", "System overheating"),
        new ErrorType("164", "Battery Low Power", "Low battery warning")
    };
    
    public TestErrorSender(Context context) {
        mContext = context;
        mRandom = new Random();
    }
    
    /**
     * 发送随机错误广播
     */
    public void sendRandomError() {
        ErrorType errorType = ERROR_TYPES[mRandom.nextInt(ERROR_TYPES.length)];
        sendErrorBroadcast(errorType.code, errorType.summary);
    }
    
    /**
     * 发送指定错误码的广播
     */
    public void sendErrorBroadcast(String errorCode, String summary) {
        try {
            Intent intent = new Intent(ACTION_ERROR_INFO);
            intent.putExtra(KEY_ERROR_CODE, errorCode);
            intent.putExtra(KEY_ERROR_TIME, getCurrentTimestamp());
            intent.putExtra(KEY_ERROR_SUMMARY, summary);
            
            // 发送带权限的广播
            mContext.sendBroadcast(intent, PERMISSION_RECEIVE_BROADCASTS);
            
            Log.i(TAG, "Error broadcast sent - Code: " + errorCode + ", Summary: " + summary);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to send error broadcast", e);
        }
    }
    
    /**
     * 发送SD卡错误（模拟EventProvider的sendTestSdCardError）
     */
    public void sendSdCardError() {
        sendErrorBroadcast("92", "SD Card Read/Write Fail - Test from TestApp");
    }
    
    /**
     * 发送摄像头错误
     */
    public void sendCameraError() {
        String[] cameraCodes = {"51", "52", "61", "62", "71", "72"};
        String code = cameraCodes[mRandom.nextInt(cameraCodes.length)];
        sendErrorBroadcast(code, "Camera error - Test from TestApp");
    }
    
    /**
     * 发送传感器错误
     */
    public void sendSensorError() {
        String[] sensorCodes = {"31", "41"};
        String code = sensorCodes[mRandom.nextInt(sensorCodes.length)];
        sendErrorBroadcast(code, "Sensor error - Test from TestApp");
    }
    
    /**
     * 发送网络错误
     */
    public void sendNetworkError() {
        String[] networkCodes = {"100", "101", "102", "121", "131"};
        String code = networkCodes[mRandom.nextInt(networkCodes.length)];
        sendErrorBroadcast(code, "Network error - Test from TestApp");
    }
    
    /**
     * 发送系统错误
     */
    public void sendSystemError() {
        String[] systemCodes = {"13", "21", "155", "164"};
        String code = systemCodes[mRandom.nextInt(systemCodes.length)];
        sendErrorBroadcast(code, "System error - Test from TestApp");
    }
    
    /**
     * 批量发送错误（压力测试）
     */
    public void sendBatchErrors(int count, long intervalMs) {
        new Thread(() -> {
            for (int i = 0; i < count; i++) {
                sendRandomError();
                try {
                    Thread.sleep(intervalMs);
                } catch (InterruptedException e) {
                    break;
                }
            }
        }).start();
    }
    
    /**
     * 获取当前时间戳（EventProvider格式）
     */
    private String getCurrentTimestamp() {
        return new SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(new Date());
    }
    
    /**
     * 获取所有支持的错误类型
     */
    public static ErrorType[] getAllErrorTypes() {
        return ERROR_TYPES.clone();
    }
    
    /**
     * 错误类型定义
     */
    public static class ErrorType {
        public final String code;
        public final String description;
        public final String summary;
        
        public ErrorType(String code, String description, String summary) {
            this.code = code;
            this.description = description;
            this.summary = summary;
        }
        
        @Override
        public String toString() {
            return code + " - " + description;
        }
    }
}
