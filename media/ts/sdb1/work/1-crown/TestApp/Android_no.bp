// TestApp Android_no.bp - AppInfo测试应用
android_app {
    name: "TestApp",
    vendor: true,
    privileged: true,
    certificate: "platform",
    platform_apis: true,  // 使用平台API访问隐藏API

    srcs: ["app/src/main/java/**/*.java"],
    resource_dirs: ["app/src/main/res"],
    manifest: "app/src/main/AndroidManifest.xml",

    static_libs: [
        "androidx.appcompat_appcompat",
        "androidx.annotation_annotation",
        "com.google.android.material_material",
        "androidx-constraintlayout_constraintlayout",
    ],

    // sdk_version: "current", // 移除，与platform_apis冲突
    min_sdk_version: "30",
    target_sdk_version: "33",

    optimize: {
        enabled: false,
    },

    system_ext_specific: false,
}
