# ConfigTool 受保护广播测试指南

## 测试目的

验证修改后的 ConfigTool 受保护广播机制是否正常工作，包括：
1. 第三方应用能否正常连接 ConfigTool 服务
2. 配置变更广播是否能正确接收
3. 权限保护是否生效
4. 系统错误日志是否消失

## 测试环境要求

### 1. ConfigTool 应用
- 已安装修改后的 ConfigTool 应用（包含受保护广播修改）
- ConfigTool 服务正在运行

### 2. TestApp 应用
- 已编译并安装本测试应用
- 包含 `configtool.api.jar` 依赖
- 已声明 `com.thundercomm.configtool.permission.CONFIG_CHANGED` 权限

## 测试步骤

### 步骤 1: 编译和安装

1. **编译 TestApp**
   ```bash
   cd /media/ts/sdb1/work/1-crown/TestApp
   ./gradlew assembleDebug
   ```

2. **安装到设备**
   ```bash
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

3. **确认 ConfigTool 服务运行**
   ```bash
   adb shell ps | grep configtool
   ```

### 步骤 2: 基础连接测试

1. **启动 TestApp**
   - 打开 TestApp 应用
   - 观察日志输出

2. **检查初始连接**
   - 应该看到 "ConfigTool binding initiated..." 
   - 等待 "ConfigTool service connected successfully!" 消息

3. **测试连接状态**
   - 点击 "Test CT" 按钮
   - 应该显示 "ConfigTool service is connected and ready!"

### 步骤 3: 配置读取测试

1. **点击 "Read" 按钮**
2. **验证输出**
   - 应该显示各种配置项的值
   - 例如：`logging = true`, `agps = true` 等
   - 不存在的键应该返回错误信息

### 步骤 4: 配置写入和广播测试

1. **点击 "Write" 按钮**
2. **观察日志输出**
   - 应该显示 "Write configuration successful!"
   - 应该显示 "Watch for configuration change broadcast..."

3. **验证广播接收**
   - 应该收到 "Configuration changed broadcast received!" 消息
   - 应该显示修改和添加的字段信息
   - 应该显示 "Broadcast test PASSED - Protected broadcast working!"

### 步骤 5: 系统日志验证

1. **检查系统日志**
   ```bash
   adb logcat | grep "non-protected broadcast"
   ```
   - 应该没有 "non-protected broadcast" 错误

2. **检查 ConfigTool 日志**
   ```bash
   adb logcat | grep ConfigTool
   ```
   - 应该看到正常的配置操作日志

## 预期结果

### ✅ 成功指标

1. **服务连接成功**
   - TestApp 能成功连接到 ConfigTool 服务
   - "Read" 和 "Write" 按钮变为可用状态

2. **配置操作正常**
   - 能够读取现有配置项
   - 能够写入新的配置数据

3. **广播接收正常**
   - 写入配置后能收到配置变更广播
   - 广播包含正确的修改和添加字段信息

4. **权限保护生效**
   - 系统日志中没有 "non-protected broadcast" 错误
   - 只有有权限的应用能接收广播

### ❌ 失败指标

1. **连接失败**
   - 服务连接超时或失败
   - "Read" 和 "Write" 按钮保持禁用状态

2. **权限错误**
   - 出现权限相关的错误日志
   - 广播接收失败

3. **系统错误持续**
   - 仍然出现 "non-protected broadcast" 错误

## 故障排除

### 问题 1: 服务连接失败
**可能原因:**
- ConfigTool 应用未安装或服务未启动
- 权限声明错误

**解决方法:**
```bash
# 检查 ConfigTool 是否安装
adb shell pm list packages | grep configtool

# 启动 ConfigTool 服务
adb shell am startservice com.thundercomm.configtool/.ConfigToolService

# 检查权限
adb shell dumpsys package com.thundercomm.testapp | grep permission
```

### 问题 2: 广播接收失败
**可能原因:**
- 权限未正确声明
- 广播接收器注册失败

**解决方法:**
- 检查 AndroidManifest.xml 中的权限声明
- 查看详细的错误日志

### 问题 3: 权限错误
**可能原因:**
- 应用签名不匹配
- 权限保护级别设置错误

**解决方法:**
- 确认两个应用使用相同的签名
- 检查权限的 protectionLevel 设置

## 测试验证清单

- [ ] TestApp 成功连接到 ConfigTool 服务
- [ ] 能够读取配置数据
- [ ] 能够写入配置数据
- [ ] 收到配置变更广播
- [ ] 广播包含正确的字段信息
- [ ] 系统日志中没有 "non-protected broadcast" 错误
- [ ] 应用运行稳定，无崩溃

## 日志示例

### 成功的测试日志
```
[10:30:15] TestApp started - AppInfo & ConfigTool Testing Ready
[10:30:15] Package: com.thundercomm.testapp
[10:30:15] === Initializing ConfigTool ===
[10:30:15] ConfigTool binding initiated...
[10:30:16] 🔗 ConfigTool service connected successfully!
[10:30:20] === Testing Read Configuration ===
[10:30:20] logging = true
[10:30:20] agps = true
[10:30:20] ✅ Read configuration test completed
[10:30:25] === Testing Write Configuration ===
[10:30:25] ✅ Write configuration successful!
[10:30:25] 🔔 Watch for configuration change broadcast...
[10:30:26] 📢 Configuration changed broadcast received!
[10:30:26] ✅ Broadcast test PASSED - Protected broadcast working!
```

这个测试指南提供了完整的测试流程来验证 ConfigTool 受保护广播机制的正确性。
