{"appinfo": [{"package_name": "com.thundercomm.testapp", "app_name": "TestApp", "description": "AppInfo configuration test application", "permissions": ["android.permission.POST_NOTIFICATIONS", "android.permission.BLUETOOTH_CONNECT", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.BLUETOOTH_SCAN", "android.permission.ACCESS_BACKGROUND_LOCATION", "android.permission.MANAGE_EXTERNAL_STORAGE", "android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"], "doze_whitelist": true, "auto_start": true, "enabled": true}]}