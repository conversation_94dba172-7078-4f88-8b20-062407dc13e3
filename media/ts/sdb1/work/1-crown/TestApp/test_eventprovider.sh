#!/bin/bash

# EventProvider广播测试脚本
# 用于测试TestApp是否能正确接收EventProvider的错误广播

echo "=== EventProvider广播接收测试脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查ADB连接
echo -e "${BLUE}1. 检查ADB连接...${NC}"
if ! adb devices | grep -q "device$"; then
    echo -e "${RED}❌ 没有检测到Android设备，请确保设备已连接并启用USB调试${NC}"
    exit 1
fi
echo -e "${GREEN}✅ ADB连接正常${NC}"
echo ""

# 检查EventProvider是否安装
echo -e "${BLUE}2. 检查EventProvider是否安装...${NC}"
if ! adb shell pm list packages | grep -q "com.thundercomm.eventprovider"; then
    echo -e "${RED}❌ EventProvider未安装${NC}"
    exit 1
fi
echo -e "${GREEN}✅ EventProvider已安装${NC}"

# 检查TestApp是否安装
echo -e "${BLUE}3. 检查TestApp是否安装...${NC}"
if ! adb shell pm list packages | grep -q "com.thundercomm.testapp"; then
    echo -e "${RED}❌ TestApp未安装${NC}"
    exit 1
fi
echo -e "${GREEN}✅ TestApp已安装${NC}"
echo ""

# 检查权限
echo -e "${BLUE}4. 检查权限配置...${NC}"
echo "检查EventProvider权限定义..."
if adb shell pm list permissions | grep -q "com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS"; then
    echo -e "${GREEN}✅ EventProvider权限已定义${NC}"
else
    echo -e "${RED}❌ EventProvider权限未定义${NC}"
fi

echo "检查TestApp权限声明..."
if adb shell dumpsys package com.thundercomm.testapp | grep -q "com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS"; then
    echo -e "${GREEN}✅ TestApp已声明接收权限${NC}"
else
    echo -e "${RED}❌ TestApp未声明接收权限${NC}"
fi
echo ""

# 启动应用
echo -e "${BLUE}5. 启动应用...${NC}"
echo "启动EventProvider..."
adb shell am start -n com.thundercomm.eventprovider/.MainActivity > /dev/null 2>&1
sleep 2

echo "启动TestApp..."
adb shell am start -n com.thundercomm.testapp/.MainActivity > /dev/null 2>&1
sleep 2
echo -e "${GREEN}✅ 应用已启动${NC}"
echo ""

# 清除日志
echo -e "${BLUE}6. 清除旧日志...${NC}"
adb logcat -c
echo -e "${GREEN}✅ 日志已清除${NC}"
echo ""

# 开始监听日志
echo -e "${BLUE}7. 开始监听广播接收日志...${NC}"
echo -e "${YELLOW}请在EventProvider应用中点击'Test SD Card Error'按钮来触发测试广播${NC}"
echo -e "${YELLOW}或者按Ctrl+C停止监听并手动发送测试广播${NC}"
echo ""

# 启动日志监听（后台）
adb logcat | grep -E "(ErrorInfoSender|ErrorInfoReceiver|TestApp)" &
LOGCAT_PID=$!

# 等待用户输入或自动发送测试广播
echo -e "${BLUE}选择测试方式:${NC}"
echo "1. 手动测试 - 在EventProvider中点击测试按钮"
echo "2. 自动测试 - 脚本发送模拟广播"
echo "3. 退出"
echo ""
read -p "请选择 (1/2/3): " choice

case $choice in
    1)
        echo -e "${YELLOW}请在EventProvider应用中点击测试按钮，然后按任意键继续...${NC}"
        read -n 1 -s
        ;;
    2)
        echo -e "${BLUE}发送模拟广播...${NC}"
        # 发送模拟的EventProvider错误广播
        adb shell am broadcast \
            -a "yellowstone.ssolapp.ERROR_INFO" \
            --es "KEY_ERROR_CODE" "92" \
            --es "KEY_ERROR_TIME" "$(date +%Y%m%d%H%M%S)" \
            --es "KEY_ERROR_SUMMARY" "Test broadcast from script" \
            --receiver-permission "com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS"
        
        echo -e "${GREEN}✅ 模拟广播已发送${NC}"
        sleep 3
        ;;
    3)
        echo -e "${YELLOW}退出测试${NC}"
        kill $LOGCAT_PID 2>/dev/null
        exit 0
        ;;
    *)
        echo -e "${RED}无效选择${NC}"
        kill $LOGCAT_PID 2>/dev/null
        exit 1
        ;;
esac

# 停止日志监听
kill $LOGCAT_PID 2>/dev/null

echo ""
echo -e "${BLUE}8. 检查测试结果...${NC}"

# 检查最近的日志
echo "检查ErrorInfoReceiver日志..."
if adb logcat -d | grep -q "ErrorInfoReceiver.*EventProvider Error Received"; then
    echo -e "${GREEN}✅ TestApp成功接收到EventProvider广播${NC}"
    
    # 显示接收到的错误信息
    echo ""
    echo -e "${BLUE}接收到的错误信息:${NC}"
    adb logcat -d | grep "ErrorInfoReceiver" | tail -10
    
else
    echo -e "${RED}❌ TestApp未接收到EventProvider广播${NC}"
    echo ""
    echo -e "${YELLOW}可能的原因:${NC}"
    echo "1. 权限配置问题"
    echo "2. 广播接收器未正确注册"
    echo "3. EventProvider未发送广播"
    echo ""
    echo -e "${BLUE}调试信息:${NC}"
    echo "EventProvider发送日志:"
    adb logcat -d | grep "ErrorInfoSender" | tail -5
    echo ""
    echo "TestApp接收日志:"
    adb logcat -d | grep "TestApp" | tail -5
fi

echo ""
echo -e "${BLUE}9. 测试完成${NC}"
echo ""
echo -e "${YELLOW}如需重新测试，请重新运行此脚本${NC}"
echo -e "${YELLOW}如需查看详细日志，请运行: adb logcat | grep -E '(ErrorInfoSender|ErrorInfoReceiver|TestApp)'${NC}"
