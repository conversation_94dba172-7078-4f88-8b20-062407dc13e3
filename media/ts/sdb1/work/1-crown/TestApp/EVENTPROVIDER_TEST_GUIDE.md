# EventProvider广播接收和AIDL发送测试指南

## 📋 **测试概述**

TestApp现在已经集成了完整的EventProvider测试功能：
1. **广播接收功能** - 接收EventProvider发送的错误广播
2. **AIDL发送功能** - 通过AIDL接口发送SD卡错误通知（模拟EventProvider的sendTestSdCardError）

## 🏗️ **实现的功能**

### 1. **权限配置**
- 在AndroidManifest.xml中添加了接收EventProvider广播的权限：
  ```xml
  <uses-permission android:name="com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS" />
  ```

### 2. **广播接收器**
- **ErrorInfoReceiver**: 静态注册的广播接收器，接收`yellowstone.ssolapp.ERROR_INFO`广播
- **本地广播接收器**: 在MainActivity中动态注册，用于显示接收到的错误信息

### 3. **错误码解析**
ErrorInfoReceiver能够解析所有硬件错误码并提供友好的错误描述：

| 错误码 | 硬件单元 | 错误描述 |
|--------|----------|----------|
| 13 | MCU | MCU No Response |
| 21 | LED | LED Control Unavailable |
| 31 | G-Sensor | G-Sensor Unavailable |
| 41 | Gyro Sensor | Gyro Sensor Unavailable |
| 51/52 | Front Camera | Unavailable/No Signal |
| 61/62 | Incabin Camera | Unavailable/No Signal |
| 70/71/72 | Rear Camera | Not Detected/Unavailable/No Signal |
| 80/81/82 | Option Camera | Not Detected/Unavailable/No Signal |
| 90/91/92 | SD Card | No SD Card/Unavailable/Read Write Fail |
| 100/101/102 | LTE Module | No SIM/Unavailable/Network Unavailable |
| 111/112 | GNSS | Fail Unavailable/DR Algorithm Unavailable |
| 121 | BT | BT Unavailable |
| 131 | WiFi | WiFi Unavailable |
| 141 | I/F Box Unit | I/F Box Unit Unavailable |
| 155 | Temperature | CPU High Temperature |
| 164 | Voltage | Battery Low Power |

### 4. **EventBrokerClient AIDL客户端**
- **EventBrokerClient**: 连接EventBroker服务的AIDL客户端
- **SD卡错误发送**: 模拟EventProvider的sendTestSdCardError方法
- **事件格式**: 完全兼容EventProvider的事件格式

### 5. **UI集成**
- 添加了"Test EP"按钮用于启动EventProvider广播测试
- 添加了"Send Err"按钮用于发送测试错误广播
- 添加了"SD AIDL"按钮用于通过AIDL发送SD卡错误
- 实时显示接收到的错误信息在日志区域
- Toast提示显示错误描述

## 🧪 **测试步骤**

### 步骤1: 准备测试环境
1. **确保EventProvider正在运行**
   ```bash
   adb shell am start -n com.thundercomm.eventprovider/.MainActivity
   ```

2. **启动TestApp**
   ```bash
   adb shell am start -n com.thundercomm.testapp/.MainActivity
   ```

3. **检查权限状态**
   ```bash
   adb shell dumpsys package com.thundercomm.testapp | grep permissions
   ```

### 步骤2: 启动广播监听
1. 在TestApp中点击"Test EP"按钮
2. 查看日志显示"Waiting for EventProvider broadcasts..."
3. 确认广播接收器已注册

### 步骤3: 测试功能

#### 测试1: 广播接收功能
在EventProvider应用中触发错误广播：
1. 打开EventProvider应用
2. 点击"Test SD Card Error"按钮
3. 观察TestApp是否接收到广播

#### 测试2: 广播发送功能
在TestApp中发送错误广播：
1. 点击"Send Err"按钮
2. TestApp会发送随机错误广播
3. 观察TestApp自己是否接收到广播

#### 测试3: AIDL发送功能（重点测试）
在TestApp中通过AIDL发送SD卡错误：
1. 等待"SD AIDL"按钮变为可用（EventBroker连接成功）
2. 点击"SD AIDL"按钮
3. 观察日志显示AIDL发送过程

### 步骤4: 验证接收结果
在TestApp中应该看到类似以下日志：
```
🚨 EventProvider Error Received!
  Error Code: 92
  Error Description: SD Card Read/Write Fail
  Error Time: 20241205143022
  Received Time: 2024-12-05 14:30:22.123
✅ EventProvider broadcast test PASSED!
```

## 🔍 **故障排除**

### 问题1: 收不到广播
**可能原因**:
1. 权限未正确声明
2. EventProvider未运行
3. 广播接收器未注册

**解决方案**:
```bash
# 检查权限
adb shell dumpsys package com.thundercomm.testapp | grep "com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS"

# 检查EventProvider是否运行
adb shell ps | grep eventprovider

# 检查广播接收器注册
adb shell dumpsys activity broadcasts | grep ErrorInfoReceiver
```

### 问题2: 权限被拒绝
**检查方法**:
```bash
# 检查权限定义
adb shell pm list permissions | grep thundercomm

# 检查应用权限状态
adb shell dumpsys package com.thundercomm.testapp | grep permissions
```

### 问题3: EventProvider未发送广播
**检查方法**:
```bash
# 查看EventProvider日志
adb logcat | grep "ErrorInfoSender"

# 查看广播发送日志
adb logcat | grep "Broadcast sent with permission"
```

## 📊 **测试验证点**

### ✅ **成功标准**
1. TestApp能够接收到EventProvider发送的错误广播
2. 错误码能够正确解析为友好的错误描述
3. 广播参数（错误码、时间、摘要）能够正确提取
4. UI能够实时显示接收到的错误信息
5. Toast提示能够正常显示

### ⚠️ **注意事项**
1. **权限必须正确声明**: 缺少权限会导致静默失败
2. **EventProvider必须运行**: 确保EventProvider服务正在运行
3. **广播Action匹配**: 确保接收器监听正确的Action
4. **线程安全**: 广播接收在后台线程，UI更新需要切换到主线程

## 🎯 **扩展测试**

### 测试不同错误类型
可以通过修改EventProvider的测试代码来发送不同类型的错误：

```java
// 在EventProvider的MainActivity中
// 测试不同的硬件错误
sendTestAbnormalResponse(mEventBroker, 
    EventType.HardwareRuntimeError.HwId.FRONT_CAMERA, 
    1, 51); // Front Camera Unavailable

sendTestAbnormalResponse(mEventBroker, 
    EventType.HardwareRuntimeError.HwId.BT, 
    1, 121); // BT Unavailable
```

### 性能测试
```bash
# 发送大量广播测试性能
for i in {1..100}; do
    adb shell am broadcast -a yellowstone.ssolapp.ERROR_INFO \
        --es KEY_ERROR_CODE "92" \
        --es KEY_ERROR_TIME "$(date +%Y%m%d%H%M%S)" \
        --es KEY_ERROR_SUMMARY "Test $i"
done
```

## 🎉 **测试完成标志**

当看到以下内容时，表示测试成功：
1. TestApp日志显示"🚨 EventProvider Error Received!"
2. 错误描述正确显示（如"SD Card Read/Write Fail"）
3. Toast提示显示错误信息
4. 日志显示"✅ EventProvider broadcast test PASSED!"

这证明EventProvider的权限控制广播机制工作正常，TestApp能够成功接收和处理EventProvider发送的错误广播。
