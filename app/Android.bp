// Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
// All Rights Reserved.
// Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
//
// Not a contribution.
android_app {
    name: "EventProvider",
    srcs: ["src/main/java/**/*.java"],
    vendor: true,
    platform_apis: true,
    privileged: true,
    sdk_version : "vendor_current",
    certificate: "platform",
    static_libs: [
        "androidx-constraintlayout_constraintlayout",
        "androidx.appcompat_appcompat",
        "com.google.android.material_material",
        "eventbroker-v1",
        "config-api",
        "vendor.thundercomm.hardware.tsnv-V1.0-java",
        "eventbroker-gson-lib",
    ],
    resource_dirs: ["src/main/res"],
    manifest: "src/main/AndroidManifest.xml",
    optimize: {
        enabled : false,
        proguard_flags_files: ["proguard-rules.pro"],
    },
    required: [
        "privapp_whitelist_com.thundercomm.eventprovider",
    ],
}

java_import {
    name: "eventbroker-v1",
    jars: ["libs/vendor.thundercomm.eventbroker-V1-java.jar"],
    sdk_version: "current",
}

java_import {
    name: "config-api",
    jars: ["libs/configtool.api.jar"],
    sdk_version: "current",
}

java_import {
    name: "eventbroker-gson-lib",
    jars: ["libs/gson.jar"],
    sdk_version : "current",
}

prebuilt_etc {
    name: "privapp_whitelist_com.thundercomm.eventprovider",
    sub_dir: "permissions",
    src: "com.thundercomm.eventprovider.xml",
    vendor: true,
    filename_from_src: true,
}
