<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_main.xml">
        <config>
          <theme>@android:style/Theme.Material.Light</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/main_activity.xml">
        <config>
          <theme>@style/Theme.PuzzleAmple</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="806d8ffc-ef97-43ce-b69d-61f6d79d82bb" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/app/libs/fileencrypt.api.jar" beforeDir="false" afterPath="$PROJECT_DIR$/app/libs/fileencrypt.api.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/thundercomm/configtool/ConfigToolService.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/thundercomm/configtool/ConfigToolService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/thundercomm/configtool/utils/Constants.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/thundercomm/configtool/utils/Constants.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$/configtoolinterface">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ConfigTool" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ConfigTool" type="f1a62948:ProjectNode" />
                <item name="configtoolinterface" type="2d1252cf:ModuleNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ConfigTool" type="f1a62948:ProjectNode" />
                <item name="configtoolinterface" type="2d1252cf:ModuleNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ConfigTool" type="f1a62948:ProjectNode" />
                <item name="configtoolinterface" type="2d1252cf:ModuleNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="other" type="c8890929:TasksNode$1" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="GenerateSignedApkSettings">
    <option name="KEY_STORE_PATH" value="$PROJECT_DIR$/../../../jks/paymentsdk.jks" />
    <option name="KEY_ALIAS" value="paymentsdk" />
    <option name="REMEMBER_PASSWORDS" value="true" />
    <option name="BUILD_TARGET_KEY" value="apk" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2lB1EzoIJXpHFQ0nRjqw6Renum1" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ApkExportedModule&quot;: &quot;PuzzleAmple.app&quot;,
    &quot;ExportApk.ApkPathForPuzzleAmple.app&quot;: &quot;/media/ts/sdb1/work/1-crown/configTool/1-ConfigTool/app&quot;,
    &quot;Gradle.ConfigTool:configtoolinterface [makeJar].executor&quot;: &quot;Run&quot;,
    &quot;KEY_ANDROID_SOURCE_PATH/media/ts/sdb1/work/1-crown/3-crown_3.2/1-vendor/LINUX/android/vendor/thundercomm/apps/ConfigTool&quot;: &quot;/media/ts/sdb1/work/1-crown/3-crown_3.2/1-vendor/LINUX/android&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;com.google.services.firebase.aqiPopupShown&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;/media/ts/sdb1/work/1-crown/3-crown_3.2/1-vendor/LINUX/android/vendor/thundercomm/apps/ConfigTool/configtoolinterface&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.17&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.23442137&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.settingsdialog.project.gradle&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;ExportApk.BuildVariants&quot;: [
      &quot;release&quot;
    ]
  }
}</component>
  <component name="PsdUISettings">
    <option name="MODULE_TAB" value="Properties" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/configtoolinterface" />
      <recent name="$PROJECT_DIR$/app/src/libs" />
      <recent name="$PROJECT_DIR$/app/src" />
      <recent name="$PROJECT_DIR$/app/src/main/res" />
      <recent name="$PROJECT_DIR$/app/libs" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="demo.ht.com.puzzleample" />
    </key>
  </component>
  <component name="RunManager" selected="Gradle.ConfigTool:configtoolinterface [makeJar]">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="ConfigTool.app.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ConfigTool:configtoolinterface [makeJar]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/configtoolinterface" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="makeJar" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Gradle.ConfigTool:configtoolinterface [makeJar]" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="806d8ffc-ef97-43ce-b69d-61f6d79d82bb" name="Changes" comment="" />
      <created>1724638547703</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1724638547703</updated>
    </task>
    <servers />
  </component>
</project>